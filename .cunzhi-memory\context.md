# 项目上下文信息

- 用户已创建 build_multi_system_data_v1.py 文件，实现了SFT数据的system prompt扩写功能：50%使用main_system，50%随机选择其他10个systems（各5%）。代码使用直接字符串替换方式，处理JSON数组格式数据。
- 用户已修改 build_multi_system_data.py 脚本，实现了SFT数据的system prompt扩写功能：50%使用main_system，50%随机选择其他10个systems（各5%）。新增了extract_role_and_type函数提取角色和类型，convert_sft_data函数处理数据转换，process_sft_data函数处理整个流程。输入文件为sft_data.json，输出文件为multi_system_sft_data.json。
- 用户已修改 build_multi_system_data.py 脚本，移除了占位符处理逻辑：1)删除了extract_role_and_type函数 2)简化了convert_sft_data函数，直接使用selected_system而不进行format格式化 3)保持权重分配不变(main_system 50%，其他systems各5%) 4)保持文件路径和其他处理逻辑不变。修改后的逻辑更简洁，直接使用预定义的系统提示字符串。
