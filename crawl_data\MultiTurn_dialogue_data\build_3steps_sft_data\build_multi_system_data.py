import os
import json
import random
import re
from multiprocessing import Pool
from collections import Counter
import sys
import copy


# AI解析格式质检规则
def check_format_for_ana(string):
    flag = True
    # 格式不合法
    pattern = r"<title>(.*?)</title>"
    match = re.findall(pattern, string)
    if match != ['考查点', '分析', '总结拓展']:
        flag  = False
    return flag


def extract_role_and_type(input_text):
    # 提取角色：居里夫人或达尔文
    role_match = re.search(r'扮演(居里夫人|达尔文)', input_text)
    role = role_match.group(1) if role_match else "居里夫人"

    # 提取类型：小行动或冷知识
    type_match = re.search(r'按(小行动|冷知识)进行', input_text)
    type_val = type_match.group(1) if type_match else "冷知识"

    return role, type_val

def convert_sft_data(item):
    # 提取角色和类型
    role, type_val = extract_role_and_type(item["input"])

    # 权重分配：main_system 50%, 其他10个systems各5%
    weights = [0.5] + [0.05] * 10
    choices = [main_system] + systems
    selected_system = random.choices(choices, weights=weights)[0]

    # 将选中的system中的占位符替换为具体值
    formatted_system = selected_system.format(role=role, type=type_val)

    # 找到原始input中的任务描述+角色扮演部分（从开头到第一个"# 核心任务:"）
    input_text = item["input"]
    core_task_index = input_text.find("# 核心任务:")

    if core_task_index != -1:
        # 保留"# 核心任务:"及之后的内容
        remaining_content = input_text[core_task_index:]
        # 构建新的input
        new_input = formatted_system + "\n\n" + remaining_content
    else:
        # 如果找不到"# 核心任务:"，则直接替换整个input
        new_input = formatted_system

    return {
        "input": new_input,
        "target": item["target"]
    }, selected_system

def convert(line):
    data = json.loads(line)

    d = {}
    d['id'] = data['id']
    if random.random() > 0.5:
        system = random.choice(systems)
        data["query"] = data["query"].replace(main_system, system)
    else:
        system = main_system
    d['input'] = data["query"].strip()
    d['target'] = data['answer'].strip()

    if check_format(data['answer']):
        return d, system
    else:
        return None


# 处理SFT数据的路径配置
input_file = r'D:\ProJects\kexue\crawl_data\MultiTurn_dialogue_data\build_3steps_sft_data\sft_total_data.json'
output_file = r'D:\ProJects\kexue\crawl_data\MultiTurn_dialogue_data\build_3steps_sft_data\multi_system_3steps_sft_data.json'

# data_dir = '/work1/data/fanzhang39/0630/jx/res'
# save_file = '/work1/data/fanzhang39/projects/kexue/dump/0707/答疑辅导/答疑辅导_AI解析.json'
check_format = check_format_for_ana
main_system = "你是一名专精于小学科学教育的课程分析专家和多分类问题的专家。"
systems = [
    "你是一位精通小学科学教学内容分析，并擅长解决多分类任务的专家。",
    "你的专业领域涵盖小学科学课程解析，以及信息的多维度归类。",
    "你扮演着一名资深的小学科学教育顾问角色，专门负责课程内容的分析与多项归类。",
    "你集小学科学课程评估与多分类问题解决能力于一身，是一位复合型专家。",
    "你拥有对小学科学教育进行深度课程分析的专业知识，并且是一位处理多分类问题的能手。",
    "你在小学科学教育的课程分析方面是权威，同时也是解决多分类挑战的专家。",
    "作为一名资深专家，你能够运用科学教育知识对课程进行分析，并完成复杂的多分类任务。",
    "你的专长在于剖析小学科学的教学内容，并将其精确地匹配到多个预设类别中。",
    "你是小学科学课程分析及多任务分类领域的资深专家。",
    "你擅长分析小学科学的教学大纲，并能熟练地为内容进行多标签分类。"
]

# 处理SFT数据
def process_sft_data():
    print(f"Processing SFT data from: {input_file}")

    # 读取原始数据
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    print(f"Total records: {len(data)}")

    res = []
    all_systems = []

    for item in data:

        converted_item, system = convert_sft_data(item)
        res.append(converted_item)
        all_systems.append(system)


    # 保存结果
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(res, f, ensure_ascii=False, indent=2)

    print(f"Processed {len(res)} records")
    print(f"Output saved to: {output_file}")

    # 统计系统使用情况
    system_counter = Counter(all_systems)
    print("\nSystem usage statistics:")
    for system, count in system_counter.items():
        percentage = count / len(res) * 100
        system_name = "main_system" if system == main_system else f"system_{systems.index(system)+1}"
        print(f"{system_name}: {count} ({percentage:.1f}%)")


# def process_original_data():
#     print(data_dir)
#
#     total = 0
#     res = []
#     all_systems = []
#     files = os.listdir(data_dir)
#     files.sort()
#     for file in files:
#         print(file)
#         data_path = os.path.join(data_dir, file)
#         f = open(data_path, encoding='utf-8')
#         with Pool(64) as p:
#             all_data = p.imap(convert, f)
#             # print(len(all_data))
#             for data in all_data:
#                 total += 1
#                 if data:
#                     data, system = data
#                     all_systems.append(system)
#                     res.append(data)
#
#     print(len(res), total)
#     with open(save_file, 'w', encoding='utf-8') as f:
#         for item in res:
#             f.write(json.dumps(item, ensure_ascii=False) + '\n')
#     print(save_file)
#
#     system_counter = Counter(all_systems)
#     system_counter = dict(sorted(system_counter.items(), key=lambda x: x[1]))
#     print(system_counter)
#     print([i / len(res) * 100 for i in system_counter.values()])

# 主执行逻辑
if __name__ == "__main__":
    # 处理SFT数据
    process_sft_data()

