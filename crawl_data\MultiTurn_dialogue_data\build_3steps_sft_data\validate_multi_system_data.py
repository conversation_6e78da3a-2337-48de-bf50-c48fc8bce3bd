import json
import re
from collections import Counter

def extract_role_and_type(input_text):
    """从input文本中提取角色和类型信息"""
    # 提取角色：居里夫人或达尔文 - 支持多种表达方式
    role_patterns = [
        r'扮演(居里夫人|达尔文)',
        r'以(居里夫人|达尔文)的身份',
        r'担当(居里夫人|达尔文)',
        r'化身为(居里夫人|达尔文)',
        r'进入(居里夫人|达尔文)角色',
        r'(居里夫人|达尔文)，参考'
    ]

    role = None
    for pattern in role_patterns:
        role_match = re.search(pattern, input_text)
        if role_match:
            role = role_match.group(1)
            break

    # 提取类型：小行动或冷知识 - 支持多种表达方式
    type_patterns = [
        r'按(小行动|冷知识)进行',
        r'按(小行动|冷知识)的要求',
        r'按(小行动|冷知识)的指示',
        r'按(小行动|冷知识)类别',
        r'依照(小行动|冷知识)的要求',
        r'依据(小行动|冷知识)',
        r'(小行动|冷知识)的指示',
        r'(小行动|冷知识)的要求进行',
        r'并按(小行动|冷知识)',
        r'按照(小行动|冷知识)的要求',
        r'然后按(小行动|冷知识)',
        r'然后按照(小行动|冷知识)'
    ]

    type_val = None
    for pattern in type_patterns:
        type_match = re.search(pattern, input_text)
        if type_match:
            type_val = type_match.group(1)
            break

    return role, type_val

def identify_system_type(input_text):
    """识别使用的是哪个system"""
    # main_system的特征文本
    main_system = "你是一名专精于小学科学教育的课程分析专家和多分类问题的专家。"


    if main_system_marker in input_text:
        return "main_system"
    else:
        # 检查是否包含其他systems的特征（使用更准确的特征文本）
        systems = [
            "你是一位精通小学科学教学内容分析，并擅长解决多分类任务的专家。",
            "你的专业领域涵盖小学科学课程解析，以及信息的多维度归类。",
            "你扮演着一名资深的小学科学教育顾问角色，专门负责课程内容的分析与多项归类。",
            "你集小学科学课程评估与多分类问题解决能力于一身，是一位复合型专家。",
            "你拥有对小学科学教育进行深度课程分析的专业知识，并且是一位处理多分类问题的能手。",
            "你在小学科学教育的课程分析方面是权威，同时也是解决多分类挑战的专家。",
            "作为一名资深专家，你能够运用科学教育知识对课程进行分析，并完成复杂的多分类任务。",
            "你的专长在于剖析小学科学的教学内容，并将其精确地匹配到多个预设类别中。",
            "你是小学科学课程分析及多任务分类领域的资深专家。",
            "你擅长分析小学科学的教学大纲，并能熟练地为内容进行多标签分类。"
        ]

        for i, marker in enumerate(system_markers):
            if marker in input_text:
                return f"system_{i}"

        return "unknown"

def validate_data():
    """验证数据的正确性"""
    print("=== 数据验证报告 ===\n")

    # 先统计数据量
    original_count = 0
    with open(r'D:\ProJects\kexue\sft_data\build_sft_data_v2\sft_data.json', 'r', encoding='utf-8') as f:
        original_data = json.load(f)
        original_count = len(original_data)

    processed_count = 0
    with open(r'D:\ProJects\kexue\sft_data\build_sft_data_v2\multi_system_sft_data.json', 'r', encoding='utf-8') as f:
        processed_data = json.load(f)
        processed_count = len(processed_data)
    
    print(f"原始数据条数: {original_count}")
    print(f"处理后数据条数: {processed_count}")
    
    # 1. 统计system使用情况
    print("\n=== 1. System使用统计 ===")
    system_counter = Counter()
    
    for item in processed_data:
        system_type = identify_system_type(item['input'])
        system_counter[system_type] += 1
    
    total_count = processed_count
    print(f"总计: {total_count} 条数据")
    
    for system, count in system_counter.most_common():
        percentage = count / total_count * 100
        print(f"{system}: {count} 条 ({percentage:.1f}%)")
    
    # 检查比例是否符合预期
    main_system_percentage = system_counter.get('main_system', 0) / total_count * 100
    print(f"\nmain_system占比: {main_system_percentage:.1f}% (目标: 50%)")
    
    other_systems_count = sum(count for system, count in system_counter.items() if system.startswith('system_'))
    other_systems_percentage = other_systems_count / total_count * 100
    print(f"其他systems总占比: {other_systems_percentage:.1f}% (目标: 50%)")
    
    # 2. 验证角色和类型保持一致性
    print("\n=== 2. 角色和类型一致性验证 ===")
    
    # 创建target到原始数据的映射
    original_map = {}
    for item in original_data:
        target = item['target']
        role, type_val = extract_role_and_type(item['input'])
        original_map[target] = {'role': role, 'type': type_val}
    
    # 验证处理后数据
    role_consistent = 0
    type_consistent = 0
    both_consistent = 0
    total_matched = 0
    
    role_counter = Counter()
    type_counter = Counter()
    
    for item in processed_data:
        target = item['target']
        processed_role, processed_type = extract_role_and_type(item['input'])
        
        role_counter[processed_role] += 1
        type_counter[processed_type] += 1
        
        if target in original_map:
            total_matched += 1
            original_role = original_map[target]['role']
            original_type = original_map[target]['type']
            
            if processed_role == original_role:
                role_consistent += 1
            if processed_type == original_type:
                type_consistent += 1
            if processed_role == original_role and processed_type == original_type:
                both_consistent += 1
    
    print(f"匹配的数据条数: {total_matched}")
    print(f"角色保持一致: {role_consistent}/{total_matched} ({role_consistent/total_matched*100:.1f}%)")
    print(f"类型保持一致: {type_consistent}/{total_matched} ({type_consistent/total_matched*100:.1f}%)")
    print(f"角色和类型都一致: {both_consistent}/{total_matched} ({both_consistent/total_matched*100:.1f}%)")
    
    # 3. 角色和类型分布统计
    print("\n=== 3. 角色分布统计 ===")
    for role, count in role_counter.most_common():
        percentage = count / total_count * 100
        print(f"{role}: {count} 条 ({percentage:.1f}%)")
    
    print("\n=== 4. 类型分布统计 ===")
    for type_val, count in type_counter.most_common():
        percentage = count / total_count * 100
        print(f"{type_val}: {count} 条 ({percentage:.1f}%)")
    
    # 4. 检查是否有异常数据
    print("\n=== 5. 异常数据检查 ===")
    
    invalid_roles = [role for role in role_counter.keys() if role not in ['居里夫人', '达尔文', None]]
    invalid_types = [type_val for type_val in type_counter.keys() if type_val not in ['小行动', '冷知识', None]]
    
    if invalid_roles:
        print(f"发现异常角色: {invalid_roles}")
    else:
        print("✓ 所有角色都是有效的（居里夫人/达尔文）")
    
    if invalid_types:
        print(f"发现异常类型: {invalid_types}")
    else:
        print("✓ 所有类型都是有效的（小行动/冷知识）")
    
    # 5. 抽样检查
    print("\n=== 6. 抽样检查 ===")
    print("前3条数据的system类型:")
    for i, item in enumerate(processed_data[:3]):
        system_type = identify_system_type(item['input'])
        role, type_val = extract_role_and_type(item['input'])
        print(f"第{i+1}条: {system_type}, 角色={role}, 类型={type_val}")

    # 6. Unknown数据分析
    print("\n=== 7. Unknown数据分析 ===")
    unknown_samples = []
    for item in processed_data:
        if identify_system_type(item['input']) == 'unknown':
            # 提取任务描述部分的前100字符作为特征
            task_desc_start = item['input'].find('# 任务描述\n') + len('# 任务描述\n')
            task_desc_end = item['input'].find('\n\n# 角色扮演')
            if task_desc_end == -1:
                task_desc_end = task_desc_start + 100
            task_desc = item['input'][task_desc_start:task_desc_end][:100]
            unknown_samples.append(task_desc)

    # 统计不同的unknown类型
    unknown_types = Counter(unknown_samples)

    print(f"Unknown数据总数: {len(unknown_samples)}")
    print("Unknown类型分布:")
    for i, (desc, count) in enumerate(unknown_types.most_common(5)):
        print(f"类型{i+1} ({count}条): {desc}...")
        if i == 0:  # 显示第一种类型的完整样本
            for item in processed_data:
                if identify_system_type(item['input']) == 'unknown' and desc in item['input']:
                    print(f"\n完整样本:")
                    print(item['input'][:300] + "...")
                    break

if __name__ == "__main__":
    validate_data()
