import os
import json
import random
import re
from multiprocessing import Pool
from collections import Counter
import sys
import copy


# AI解析格式质检规则
def check_format_for_ana(string):
    flag = True
    # 格式不合法
    pattern = r"<title>(.*?)</title>"
    match = re.findall(pattern, string)
    if match != ['考查点', '分析', '总结拓展']:
        flag  = False
    return flag




def convert_sft_data(item):
    # 权重分配：main_system 50%, 其他10个systems各5%
    weights = [0.5] + [0.05] * 10
    choices = [main_system] + systems
    selected_system = random.choices(choices, weights=weights)[0]

    input_text = item["input"]
    task_index = input_text.find("# 任务")

    if task_index != -1:
        # 保留"# 任务"及之后的内容
        remaining_content = input_text[task_index:]
        # 构建新的input，直接使用选中的系统提示
        new_input = "# 角色\n" + selected_system + "\n" + remaining_content
    else:
        # 如果找不到"# 任务"，则直接使用选中的系统提示
        new_input = "# 角色\n" + selected_system

    return {
        "input": new_input,
        "target": item["target"]
    }, selected_system

def convert(line):
    data = json.loads(line)

    d = {}
    d['id'] = data['id']
    if random.random() > 0.5:
        system = random.choice(systems)
        data["query"] = data["query"].replace(main_system, system)
    else:
        system = main_system
    d['input'] = data["query"].strip()
    d['target'] = data['answer'].strip()

    if check_format(data['answer']):
        return d, system
    else:
        return None


# 处理SFT数据的路径配置
input_file = r'D:\ProJects\kexue\crawl_data\MultiTurn_dialogue_data\build_3steps_sft_data\sft_total_data.json'
output_file = r'D:\ProJects\kexue\crawl_data\MultiTurn_dialogue_data\build_3steps_sft_data\multi_system_3steps_sft_data.json'

# data_dir = '/work1/data/fanzhang39/0630/jx/res'
# save_file = '/work1/data/fanzhang39/projects/kexue/dump/0707/答疑辅导/答疑辅导_AI解析.json'
check_format = check_format_for_ana
main_system = "你是一名专精于小学科学教育的课程分析专家和多分类问题的专家。"
systems = [
    "你扮演着一名小学科学教育权威的角色，其核心专长在于设计富有启发性的多轮对话。",
    "作为一名资深专家，你在小学1-6年级科学教育领域造诣深厚，并能熟练地搭建引导式的多步骤交流。",
    "你的核心能力在于对小学科学课程的深刻理解，以及创建结构化、连续性对话的技巧。",
    "你是一位对小学科学教育了如指掌的行家，尤其擅长通过连续的、引导性的对话来启发学生。",
    "你的专长有两个方面：一是深耕小学科学教育，二是精于设计和引导连续性的师生互动对话。",
    "你是一位深谙儿童科学启蒙教育之道的专家，并且在组织多轮次、结构化的对话方面表现出色。",
    "你能够运用丰富的小学科学教育知识，去构建富有逻辑和启发性的多轮对话流程。",
    "你是小学科学教育与引导式对话构建这两个领域的复合型专家。",
    "你在小学科学教育方面拥有权威见解，并以创造引人入胜的多轮对话流程而闻名。",
    "你对小学科学怎么教、教什么了然于心，并且是个能设计出精彩多轮回合对话的高手。"
]

# 处理SFT数据
def process_sft_data():
    print(f"Processing SFT data from: {input_file}")

    # 读取原始数据
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    print(f"Total records: {len(data)}")

    res = []
    all_systems = []

    for item in data:

        converted_item, system = convert_sft_data(item)
        res.append(converted_item)
        all_systems.append(system)


    # 保存结果
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(res, f, ensure_ascii=False, indent=2)

    print(f"Processed {len(res)} records")
    print(f"Output saved to: {output_file}")

    # 统计系统使用情况
    system_counter = Counter(all_systems)
    print("\nSystem usage statistics:")
    for system, count in system_counter.items():
        percentage = count / len(res) * 100
        system_name = "main_system" if system == main_system else f"system_{systems.index(system)+1}"
        print(f"{system_name}: {count} ({percentage:.1f}%)")


# def process_original_data():
#     print(data_dir)
#
#     total = 0
#     res = []
#     all_systems = []
#     files = os.listdir(data_dir)
#     files.sort()
#     for file in files:
#         print(file)
#         data_path = os.path.join(data_dir, file)
#         f = open(data_path, encoding='utf-8')
#         with Pool(64) as p:
#             all_data = p.imap(convert, f)
#             # print(len(all_data))
#             for data in all_data:
#                 total += 1
#                 if data:
#                     data, system = data
#                     all_systems.append(system)
#                     res.append(data)
#
#     print(len(res), total)
#     with open(save_file, 'w', encoding='utf-8') as f:
#         for item in res:
#             f.write(json.dumps(item, ensure_ascii=False) + '\n')
#     print(save_file)
#
#     system_counter = Counter(all_systems)
#     system_counter = dict(sorted(system_counter.items(), key=lambda x: x[1]))
#     print(system_counter)
#     print([i / len(res) * 100 for i in system_counter.values()])

# 主执行逻辑
if __name__ == "__main__":
    # 处理SFT数据
    process_sft_data()

